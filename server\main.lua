local cds = {}
local robbing = false
local robSource = {}

if Config.oldESX then
    ESX = exports["es_extended"]:getSharedObject()
end


-- 新增函数来检查玩家是否是警察
function IsPoliceJob(xPlayer)
    if not xPlayer or not xPlayer.job then
        return false
    end
    
    for _, job in ipairs(Config.PoliceJobs) do
        if xPlayer.job.name == job then
            return true
        end
    end
    return false
end


ESX.RegisterServerCallback('Rs_idrobbery:requestStart', function(source, cb, shop)

    local time = os.time()
    local xPlayers = ESX.GetPlayers()
    local cops = 0
    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer ~= nil then
            -- 添加逻辑以检查玩家职业是否是警察
            if IsPoliceJob(xPlayer) then
                cops = cops + 1
            end
        end
    end

    if cops < Config.shops[shop].cops then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_cop'))
        cb(false)
    elseif robbing then
        TriggerClientEvent('esx:showNotification', source, _U('robbing'))
        cb(false)
    elseif cds[shop] and time < cds[shop] then
        TriggerClientEvent('esx:showNotification', source, _U('cd_time', os.date('%X', tostring(cds[shop]):sub(1, 10))))
        cb(false)
    else
        cb(true)
    end
end)

ESX.RegisterServerCallback('Rs_idrobbery:checkId', function(source, cb, input, shop)
    if robbing then
        TriggerClientEvent('esx:showNotification', source, _U('robbing'))
        cb(false)
        return
    end
    --double check cop count, in case cop count decreases after input time delay
    local xPlayers = ESX.GetPlayers()
    local cops = 0
	for i=1, #xPlayers, 1 do
		local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
		if xPlayer ~= nil then
			if IsPoliceJob(xPlayer) then
			cops = cops + 1
			end
		end
	end
    if cops < Config.shops[shop].cops then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_cop'))
        cb(false)
        return
    end
    --add source, and check whether id is valid
    local ids = mysplit(input, ',')
    local parti = {}
    for i = 1, #ids do
        local id = tonumber(ids[i])
        local xPlayer = ESX.GetPlayerFromId(id)
        if xPlayer then
            for n = 1, #parti do
                if id == parti[n] then
                    TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                    cb(false)
                    return
                end
            end
            table.insert(parti, id)
        else
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            cb(false)
            return
        end
    end
    --start rob
    cb(true)
    robbing = shop
    -- TriggerClientEvent('chat:addMessage', -1, {
            -- template = '<div style="padding: 5px 10px; font-size:20px; background: rgb(255 0 0 / 50%); border-radius: 10px;">通知: {0}正遭受抢劫<br>抢匪: ({1}) {2}</div>',
            -- args = { shop, #robbersLabel, table.concat(Sanitize(xPlayer.get('firstName')..xPlayer.get('lastName')), ', ') }
        -- })

	TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_content', shop, input))
	TriggerClientEvent('Rs_idrobbery:allStart', -1, shop, parti)
    robSource['robber'] = parti
    allRobbers = {} -- 重置记录
    for i = 1, #parti do
        table.insert(allRobbers, parti[i]) -- 记录所有参与的劫匪
    end
    print("DEBUG: 抢劫开始，记录劫匪数量:", #allRobbers)
    for i = 1, #allRobbers do
        print("DEBUG: 劫匪ID:", allRobbers[i])
    end
    mainSource = source
end)

RegisterNetEvent('Rs_idrobbery:copId')
AddEventHandler('Rs_idrobbery:copId', function(input)
    local source = source -- 确保获取触发事件的玩家ID
    local ids = mysplit(input, ',')
    local parti = {}
    
    for i = 1, #ids do
        local id = tonumber(ids[i])
        local xPlayer = ESX.GetPlayerFromId(id)
        
        -- 先检查xPlayer是否存在，再检查职业
        if not xPlayer then
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            return
        end
        
        if not IsPoliceJob(xPlayer) then
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            return
        end
        
        for n = 1, #parti do
            if id == parti[n] then
                TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                return
            end
        end
        
        table.insert(parti, id)
    end
    
    local xPlayers = ESX.GetPlayers()
    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer and IsPoliceJob(xPlayer) then
            TriggerClientEvent('Rs_idrobbery:copIdEnd', xPlayer.source, parti)
        end
    end
    
    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_cop_content', robbing, input))
    robSource['cop'] = parti
end)

-- 新增变量来记录所有参与的劫匪（包括被击毙的）
local allRobbers = {}

RegisterNetEvent('Rs_idrobbery:out')
AddEventHandler('Rs_idrobbery:out', function(killer)
    for k, v in pairs(robSource) do
        for i = 1, #v do
            if v[i] == source then
                table.remove(v, i)
                break
            end
        end
        if #v == 0 then
	        TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '警方'
            if k == 'cop' then
                winner = '劫方'
                -- 计算奖励金额，每个劫匪都获得完整金额（包括被击毙的）
                local rewardPerRobber = math.random(Config.shops[robbing].money1, Config.shops[robbing].money2)

                print("DEBUG: 劫匪获胜，开始分配奖励")
                print("DEBUG: allRobbers数量:", #allRobbers)
                print("DEBUG: 每人奖励金额:", rewardPerRobber)

                -- 给所有参与的劫匪分配奖励（包括被击毙的）
                for i = 1, #allRobbers do
                    local robberId = allRobbers[i]
                    local xPlayer = ESX.GetPlayerFromId(robberId)
                    print("DEBUG: 处理劫匪ID:", robberId, "xPlayer存在:", xPlayer ~= nil)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerRobber)
                        xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                        print("DEBUG: 给劫匪", robberId, "发放奖励", rewardPerRobber)
                    end
                end
                cds[robbing] = (os.time() + Config.shops[robbing].cd)
            end
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_end_content', robbing, winner))
            robbing = false
            robSource = {}
            allRobbers = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)



RegisterNetEvent('Rs_idrobbery:earlyExit')
AddEventHandler('Rs_idrobbery:earlyExit', function()
    TriggerClientEvent('Rs_idrobbery:allEnd', -1)
    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_cancel_content', robbing))
    cds[robbing] = (os.time() + Config.shops[robbing].cd)
    robbing = false
    robSource = {}
    allRobbers = {} -- 清空记录
    mainSource = nil
end)

AddEventHandler('playerDropped', function()
    for k, v in pairs(robSource) do
        for i = 1, #v do
            if v[i] == source then
                table.remove(v, i)
                break
            end
        end
        if #v == 0 then
	        TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '警方'
            if k == 'cop' then
                winner = '劫方'
                -- 计算奖励金额，每个劫匪都获得完整金额（包括被击毙的）
                local rewardPerRobber = math.random(Config.shops[robbing].money1, Config.shops[robbing].money2)

                print("DEBUG (playerDropped): 劫匪获胜，开始分配奖励")
                print("DEBUG (playerDropped): allRobbers数量:", #allRobbers)
                print("DEBUG (playerDropped): 每人奖励金额:", rewardPerRobber)

                -- 给所有参与的劫匪分配奖励（包括被击毙的）
                for i = 1, #allRobbers do
                    local robberId = allRobbers[i]
                    local xPlayer = ESX.GetPlayerFromId(robberId)
                    print("DEBUG (playerDropped): 处理劫匪ID:", robberId, "xPlayer存在:", xPlayer ~= nil)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerRobber)
                        xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                        print("DEBUG (playerDropped): 给劫匪", robberId, "发放奖励", rewardPerRobber)
                    end
                end
                cds[robbing] = (os.time() + Config.shops[robbing].cd)
            end
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_end_content', robbing, winner))
            robbing = false
            robSource = {}
            allRobbers = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)

ESX.RegisterServerCallback('Rs_idrobbery:getscores', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- 检查玩家是否存在
    if xPlayer then
        local playerId = xPlayer.getIdentifier()

        -- 查询数据库以获取分数数据
        MySQL.Async.fetchAll('SELECT * FROM rs_idrobbery WHERE owner = @playerId', {
            ['@playerId'] = playerId
        }, function(result)
            local scores = {}

            for _, row in pairs(result) do
                table.insert(scores, {
                    name = row.name,
                    mark = row.mark
                })
            end

            -- 返回分数数据给客户端
            cb(scores)
        end)
    else
        print('错误: 未找到玩家')
        cb(nil)
    end
end)


function Sanitize(str)
  local replacements = {
    ['&'] = '&amp;',
    ['<'] = '&lt;',
    ['>'] = '&gt;',
    ['\n'] = '<br/>'
  }

  return str
      :gsub('[&<>\n]', replacements)
      :gsub(' +', function(s)
        return ' ' .. ('&nbsp;'):rep(#s - 1)
      end)
end