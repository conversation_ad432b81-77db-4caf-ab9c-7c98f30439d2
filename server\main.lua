local cds = {}
local robbing = false
local robSource = {}

if Config.oldESX then
    ESX = exports["es_extended"]:getSharedObject()
end


-- 新增函数来检查玩家是否是警察
function IsPoliceJob(xPlayer)
    if not xPlayer or not xPlayer.job then
        return false
    end
    
    for _, job in ipairs(Config.PoliceJobs) do
        if xPlayer.job.name == job then
            return true
        end
    end
    return false
end


ESX.RegisterServerCallback('Rs_idrobbery:requestStart', function(source, cb, shop)

    local time = os.time()
    local xPlayers = ESX.GetPlayers()
    local cops = 0
    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer ~= nil then
            -- 添加逻辑以检查玩家职业是否是警察
            if IsPoliceJob(xPlayer) then
                cops = cops + 1
            end
        end
    end

    if cops < Config.shops[shop].cops then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_cop'))
        cb(false)
    elseif robbing then
        TriggerClientEvent('esx:showNotification', source, _U('robbing'))
        cb(false)
    elseif cds[shop] and time < cds[shop] then
        TriggerClientEvent('esx:showNotification', source, _U('cd_time', os.date('%X', tostring(cds[shop]):sub(1, 10))))
        cb(false)
    else
        cb(true)
    end
end)

ESX.RegisterServerCallback('Rs_idrobbery:checkId', function(source, cb, input, shop)
    if robbing then
        TriggerClientEvent('esx:showNotification', source, _U('robbing'))
        cb(false)
        return
    end
    --double check cop count, in case cop count decreases after input time delay
    local xPlayers = ESX.GetPlayers()
    local cops = 0
	for i=1, #xPlayers, 1 do
		local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
		if xPlayer ~= nil then
			if IsPoliceJob(xPlayer) then
			cops = cops + 1
			end
		end
	end
    if cops < Config.shops[shop].cops then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_cop'))
        cb(false)
        return
    end
    --add source, and check whether id is valid
    local ids = mysplit(input, ',')
    local parti = {}
    for i = 1, #ids do
        local id = tonumber(ids[i])
        local xPlayer = ESX.GetPlayerFromId(id)
        if xPlayer then
            for n = 1, #parti do
                if id == parti[n] then
                    TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                    cb(false)
                    return
                end
            end
            table.insert(parti, id)
        else
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            cb(false)
            return
        end
    end
    --start rob
    cb(true)
    robbing = shop
    -- TriggerClientEvent('chat:addMessage', -1, {
            -- template = '<div style="padding: 5px 10px; font-size:20px; background: rgb(255 0 0 / 50%); border-radius: 10px;">通知: {0}正遭受抢劫<br>抢匪: ({1}) {2}</div>',
            -- args = { shop, #robbersLabel, table.concat(Sanitize(xPlayer.get('firstName')..xPlayer.get('lastName')), ', ') }
        -- })

	TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_content', shop, input))
	TriggerClientEvent('Rs_idrobbery:allStart', -1, shop, parti)
    robSource['robber'] = parti
    allRobbers = {} -- 重置记录
    for i = 1, #parti do
        table.insert(allRobbers, parti[i]) -- 记录所有参与的劫匪
    end
    mainSource = source
end)

RegisterNetEvent('Rs_idrobbery:copId')
AddEventHandler('Rs_idrobbery:copId', function(input)
    local source = source -- 确保获取触发事件的玩家ID
    local ids = mysplit(input, ',')
    local parti = {}
    
    for i = 1, #ids do
        local id = tonumber(ids[i])
        local xPlayer = ESX.GetPlayerFromId(id)
        
        -- 先检查xPlayer是否存在，再检查职业
        if not xPlayer then
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            return
        end
        
        if not IsPoliceJob(xPlayer) then
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            return
        end
        
        for n = 1, #parti do
            if id == parti[n] then
                TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                return
            end
        end
        
        table.insert(parti, id)
    end
    
    local xPlayers = ESX.GetPlayers()
    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer and IsPoliceJob(xPlayer) then
            TriggerClientEvent('Rs_idrobbery:copIdEnd', xPlayer.source, parti)
        end
    end
    
    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_cop_content', robbing, input))
    robSource['cop'] = parti
end)

-- 新增变量来记录所有参与的劫匪（包括被击毙的）
local allRobbers = {}

RegisterNetEvent('Rs_idrobbery:out')
AddEventHandler('Rs_idrobbery:out', function(killer)
    -- 防止抢劫已经结束后继续处理
    if not robbing then
        return
    end

    local playerRemoved = false

    for k, v in pairs(robSource) do
        for i = 1, #v do
            if v[i] == source then
                table.remove(v, i)
                playerRemoved = true
                break
            end
        end
        if #v == 0 and playerRemoved then
            -- 保存allRobbers的副本，防止被清空
            local savedRobbers = {}
            for i = 1, #allRobbers do
                table.insert(savedRobbers, allRobbers[i])
            end

	        TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '警方'
            if k == 'cop' then
                winner = '劫方'
                -- 计算奖励金额，每个劫匪都获得完整金额（包括被击毙的）
                local rewardPerRobber = math.random(Config.shops[robbing].money1, Config.shops[robbing].money2)

                -- 给所有参与的劫匪分配奖励（包括被击毙的）
                for i = 1, #savedRobbers do
                    local robberId = savedRobbers[i]
                    local xPlayer = ESX.GetPlayerFromId(robberId)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerRobber)
                        xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                    end
                end
                cds[robbing] = (os.time() + Config.shops[robbing].cd)
            end
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_end_content', robbing, winner))
            robbing = false
            robSource = {}
            allRobbers = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)



RegisterNetEvent('Rs_idrobbery:earlyExit')
AddEventHandler('Rs_idrobbery:earlyExit', function()
    TriggerClientEvent('Rs_idrobbery:allEnd', -1)
    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_cancel_content', robbing))
    cds[robbing] = (os.time() + Config.shops[robbing].cd)
    robbing = false
    robSource = {}
    allRobbers = {} -- 清空记录
    mainSource = nil
end)

AddEventHandler('playerDropped', function()
    for k, v in pairs(robSource) do
        for i = 1, #v do
            if v[i] == source then
                table.remove(v, i)
                break
            end
        end
        if #v == 0 then
	        TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '警方'
            if k == 'cop' then
                winner = '劫方'
                -- 计算奖励金额，每个劫匪都获得完整金额（包括被击毙的）
                local rewardPerRobber = math.random(Config.shops[robbing].money1, Config.shops[robbing].money2)

                -- 给所有参与的劫匪分配奖励（包括被击毙的）
                for i = 1, #allRobbers do
                    local robberId = allRobbers[i]
                    local xPlayer = ESX.GetPlayerFromId(robberId)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerRobber)
                        xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                    end
                end
                cds[robbing] = (os.time() + Config.shops[robbing].cd)
            end
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, _U('rob_chat_end_content', robbing, winner))
            robbing = false
            robSource = {}
            allRobbers = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)

ESX.RegisterServerCallback('Rs_idrobbery:getscores', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- 检查玩家是否存在
    if xPlayer then
        local playerId = xPlayer.getIdentifier()

        -- 查询数据库以获取分数数据
        MySQL.Async.fetchAll('SELECT * FROM rs_idrobbery WHERE owner = @playerId', {
            ['@playerId'] = playerId
        }, function(result)
            local scores = {}

            for _, row in pairs(result) do
                table.insert(scores, {
                    name = row.name,
                    mark = row.mark
                })
            end

            -- 返回分数数据给客户端
            cb(scores)
        end)
    else
        print('错误: 未找到玩家')
        cb(nil)
    end
end)


function Sanitize(str)
  local replacements = {
    ['&'] = '&amp;',
    ['<'] = '&lt;',
    ['>'] = '&gt;',
    ['\n'] = '<br/>'
  }

  return str
      :gsub('[&<>\n]', replacements)
      :gsub(' +', function(s)
        return ' ' .. ('&nbsp;'):rep(#s - 1)
      end)
end

-- 测试命令：模拟抢劫成功
RegisterCommand('testrobbery', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- 检查是否有正在进行的抢劫
    if robbing then
        TriggerClientEvent('esx:showNotification', source, '已有抢劫正在进行中')
        return
    end

    -- 获取商店名称，默认为第一个商店
    local shopName = args[1] or '5号超商'
    if not Config.shops[shopName] then
        TriggerClientEvent('esx:showNotification', source, '商店不存在: ' .. shopName)
        return
    end

    print("DEBUG: 开始测试抢劫 - 商店:", shopName, "玩家:", source)

    -- 模拟开始抢劫
    robbing = shopName
    robSource['robber'] = {source}
    allRobbers = {source}
    mainSource = source

    print("DEBUG: 测试抢劫设置完成")
    print("DEBUG: robSource['robber']:", robSource['robber'][1])
    print("DEBUG: allRobbers:", allRobbers[1])

    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, '测试抢劫开始: ' .. shopName)
    TriggerClientEvent('Rs_idrobbery:allStart', -1, shopName, {source})

    -- 3秒后模拟抢劫成功
    Citizen.SetTimeout(3000, function()
        if robbing == shopName then
            print("DEBUG: 模拟抢劫成功")

            -- 保存allRobbers的副本
            local savedRobbers = {}
            for i = 1, #allRobbers do
                table.insert(savedRobbers, allRobbers[i])
            end

            TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '劫方'

            -- 计算奖励金额
            local rewardPerRobber = math.random(Config.shops[shopName].money1, Config.shops[shopName].money2)

            print("DEBUG: 测试抢劫成功，开始分配奖励")
            print("DEBUG: savedRobbers数量:", #savedRobbers)
            print("DEBUG: 每人奖励金额:", rewardPerRobber)

            -- 给所有参与的劫匪分配奖励
            for i = 1, #savedRobbers do
                local robberId = savedRobbers[i]
                local xPlayer = ESX.GetPlayerFromId(robberId)
                print("DEBUG: 处理劫匪ID:", robberId, "xPlayer存在:", xPlayer ~= nil)
                if xPlayer then
                    xPlayer.addAccountMoney('black_money', rewardPerRobber)
                    xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                    print("DEBUG: 给劫匪", robberId, "发放奖励", rewardPerRobber)
                end
            end

            cds[shopName] = (os.time() + Config.shops[shopName].cd)
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, '测试抢劫结束: ' .. shopName .. ' 获胜方: ' .. winner)

            robbing = false
            robSource = {}
            allRobbers = {}
            mainSource = nil
        end
    end)

    TriggerClientEvent('esx:showNotification', source, '测试抢劫已开始，3秒后自动成功')
end, false)

-- 测试命令：模拟被击毙后获得奖励
RegisterCommand('testrobberydie', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- 检查是否有正在进行的抢劫
    if robbing then
        TriggerClientEvent('esx:showNotification', source, '已有抢劫正在进行中')
        return
    end

    -- 获取商店名称，默认为第一个商店
    local shopName = args[1] or '5号超商'
    if not Config.shops[shopName] then
        TriggerClientEvent('esx:showNotification', source, '商店不存在: ' .. shopName)
        return
    end

    print("DEBUG: 开始测试抢劫(被击毙) - 商店:", shopName, "玩家:", source)

    -- 模拟开始抢劫
    robbing = shopName
    robSource['robber'] = {source}
    robSource['cop'] = {999} -- 模拟警察
    allRobbers = {source}
    mainSource = source

    TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, '测试抢劫开始(被击毙测试): ' .. shopName)
    TriggerClientEvent('Rs_idrobbery:allStart', -1, shopName, {source})

    -- 3秒后模拟劫匪被击毙，但警察也全部阵亡，劫匪获胜
    Citizen.SetTimeout(3000, function()
        if robbing == shopName then
            print("DEBUG: 模拟劫匪被击毙但最终获胜")

            -- 先移除劫匪（模拟被击毙）
            for i = 1, #robSource['robber'] do
                if robSource['robber'][i] == source then
                    table.remove(robSource['robber'], i)
                    break
                end
            end

            -- 然后移除所有警察（模拟警察全部阵亡）
            robSource['cop'] = {}

            -- 保存allRobbers的副本
            local savedRobbers = {}
            for i = 1, #allRobbers do
                table.insert(savedRobbers, allRobbers[i])
            end

            TriggerClientEvent('Rs_idrobbery:allEnd', -1)
            local winner = '劫方'

            -- 计算奖励金额
            local rewardPerRobber = math.random(Config.shops[shopName].money1, Config.shops[shopName].money2)

            print("DEBUG: 测试抢劫(被击毙)成功，开始分配奖励")
            print("DEBUG: savedRobbers数量:", #savedRobbers)
            print("DEBUG: 每人奖励金额:", rewardPerRobber)

            -- 给所有参与的劫匪分配奖励（包括被击毙的）
            for i = 1, #savedRobbers do
                local robberId = savedRobbers[i]
                local xPlayer = ESX.GetPlayerFromId(robberId)
                print("DEBUG: 处理劫匪ID:", robberId, "xPlayer存在:", xPlayer ~= nil)
                if xPlayer then
                    xPlayer.addAccountMoney('black_money', rewardPerRobber)
                    xPlayer.showNotification(_U('rob_end', rewardPerRobber))
                    print("DEBUG: 给劫匪", robberId, "发放奖励", rewardPerRobber)
                end
            end

            cds[shopName] = (os.time() + Config.shops[shopName].cd)
            TriggerClientEvent('chatMessage', -1, _U('rob_chat_title'), {255, 0, 0}, '测试抢劫结束(被击毙测试): ' .. shopName .. ' 获胜方: ' .. winner)

            robbing = false
            robSource = {}
            allRobbers = {}
            mainSource = nil
        end
    end)

    TriggerClientEvent('esx:showNotification', source, '测试抢劫(被击毙)已开始，3秒后模拟被击毙但获得奖励')
end, false)